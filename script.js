const display = document.getElementById("display")
let currentInput = "0"
let operator = null
let previousInput = null
let waitingForOperand = false

function updateDisplay() {
  display.textContent = currentInput
}

function clearDisplay() {
  currentInput = "0"
  operator = null
  previousInput = null
  waitingForOperand = false
  updateDisplay()
}

function deleteLast() {
  if (currentInput.length > 1) {
    currentInput = currentInput.slice(0, -1)
  } else {
    currentInput = "0"
  }
  updateDisplay()
}

function appendToDisplay(value) {
  if (["+", "-", "*", "/"].includes(value)) {
    inputOperator(value)
  } else if (value === ".") {
    if (currentInput.indexOf(".") === -1) {
      if (waitingForOperand) {
        currentInput = "0."
        waitingForOperand = false
      } else {
        currentInput += "."
      }
      updateDisplay()
    }
  } else {
    if (waitingForOperand) {
      currentInput = value
      waitingForOperand = false
    } else {
      if (currentInput === "0") {
        currentInput = value
      } else {
        currentInput += value
      }
    }
    updateDisplay()
  }
}

function inputOperator(nextOperator) {
  const inputValue = Number.parseFloat(currentInput)

  if (previousInput === null) {
    previousInput = inputValue
  } else if (operator) {
    const currentValue = previousInput || 0
    const newValue = calculate(currentValue, inputValue, operator)

    currentInput = String(newValue)
    previousInput = newValue
    updateDisplay()
  }

  waitingForOperand = true
  operator = nextOperator
}

function calculate(firstOperand, secondOperand, operator) {
  switch (operator) {
    case "+":
      return firstOperand + secondOperand
    case "-":
      return firstOperand - secondOperand
    case "*":
      return firstOperand * secondOperand
    case "/":
      return secondOperand !== 0 ? firstOperand / secondOperand : 0
    default:
      return secondOperand
  }
}

function calculateResult() {
  const inputValue = Number.parseFloat(currentInput)

  if (previousInput !== null && operator) {
    const newValue = calculate(previousInput, inputValue, operator)
    currentInput = String(newValue)
    previousInput = null
    operator = null
    waitingForOperand = true
    updateDisplay()
  }
}

// Keyboard support
document.addEventListener("keydown", (event) => {
  const key = event.key

  if ((key >= "0" && key <= "9") || key === ".") {
    appendToDisplay(key)
  } else if (["+", "-", "*", "/"].includes(key)) {
    appendToDisplay(key)
  } else if (key === "Enter" || key === "=") {
    event.preventDefault()
    calculateResult()
  } else if (key === "Escape" || key === "c" || key === "C") {
    clearDisplay()
  } else if (key === "Backspace") {
    deleteLast()
  }
})

// Initialize display
updateDisplay()
