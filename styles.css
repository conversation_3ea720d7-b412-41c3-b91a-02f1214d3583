* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.calculator-container {
  perspective: 1000px;
}

.calculator {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 350px;
  width: 100%;
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.calculator:hover {
  transform: rotateY(2deg) rotateX(2deg);
}

.display {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.3);
}

.display-screen {
  color: #fff;
  font-size: 2.5rem;
  font-weight: 300;
  text-align: right;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 12px;
  height: 400px;
}

.btn {
  border: none;
  border-radius: 12px;
  font-size: 1.4rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-number {
  background: linear-gradient(145deg, #f0f0f0, #d1d1d1);
  color: #333;
}

.btn-number:hover {
  background: linear-gradient(145deg, #e0e0e0, #c1c1c1);
}

.btn-operator {
  background: linear-gradient(145deg, #ff9500, #e6850e);
  color: white;
}

.btn-operator:hover {
  background: linear-gradient(145deg, #ff8500, #d6750e);
}

.btn-equals {
  background: linear-gradient(145deg, #ff9500, #e6850e);
  color: white;
  grid-row: span 2;
  font-size: 1.8rem;
}

.btn-equals:hover {
  background: linear-gradient(145deg, #ff8500, #d6750e);
}

.btn-clear {
  background: linear-gradient(145deg, #ff4757, #e84057);
  color: white;
}

.btn-clear:hover {
  background: linear-gradient(145deg, #ff3742, #d83047);
}

.btn-zero {
  grid-column: span 2;
}

/* Responsive Design */
@media (max-width: 480px) {
  .calculator {
    padding: 20px;
    max-width: 300px;
  }

  .display-screen {
    font-size: 2rem;
    min-height: 50px;
  }

  .buttons {
    height: 350px;
    gap: 10px;
  }

  .btn {
    font-size: 1.2rem;
  }

  .btn-equals {
    font-size: 1.5rem;
  }
}

@media (max-width: 360px) {
  .calculator {
    padding: 15px;
    max-width: 280px;
  }

  .display-screen {
    font-size: 1.8rem;
  }

  .buttons {
    height: 320px;
    gap: 8px;
  }

  .btn {
    font-size: 1.1rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .calculator {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Animation for button press */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.btn:active {
  animation: buttonPress 0.1s ease;
}
